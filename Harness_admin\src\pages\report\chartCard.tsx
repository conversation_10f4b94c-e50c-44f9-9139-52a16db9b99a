import { ChevronDown } from 'lucide-react';
import type { ReactNode } from 'react';

interface ChartCardProps {
  title: string;
  selectedMonth: string;
  isDropdownOpen: boolean;
  setIsDropdownOpen: (isOpen: boolean) => void;
  handleMonthSelect: (month: string) => void;
  children: ReactNode;
  monthOptions: string[];
}

const ChartCard = ({ title, selectedMonth, isDropdownOpen, setIsDropdownOpen, handleMonthSelect, children, monthOptions }: ChartCardProps) => (
  <div className="bg-white p-6 rounded-xl border border-gray-200">
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-sm font-medium text-gray-900">{title}</h3>
      <div className="relative">
        <button 
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="text-orange-500 text-sm hover:text-orange-600 flex items-center gap-1 px-3 py-1 rounded-md hover:bg-orange-50 transition-colors"
        >
          {selectedMonth} <ChevronDown className="w-3 h-3" />
        </button>
        
        {isDropdownOpen && (
          <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
            <div className="py-1">
              {monthOptions.map((month) => (
                <button
                  key={month}
                  onClick={() => handleMonthSelect(month)}
                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors ${
                    selectedMonth === month ? 'bg-orange-50 text-orange-600' : 'text-gray-700'
                  }`}
                >
                  {month}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
    {children}
  </div>
);

export default ChartCard;