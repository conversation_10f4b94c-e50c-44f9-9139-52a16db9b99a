import { Card, CardContent,  CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ChevronRight } from "lucide-react"
import { Link } from "react-router-dom"

interface MetricCardProps {
  title: string
  metric: string | number
  viewAllLink?: string
}

export function MetricCard({ title, metric, viewAllLink }: MetricCardProps) {
  return (
    <Card className="p-6 hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        <CardTitle className="text-lg font-medium text-gray-900 mb-6">
          {title}
        </CardTitle>
        <Separator className="mb-4" />
        <div className="text-4xl font-bold text-gray-900 mb-4">
          {metric}
        </div>
        {viewAllLink && (
          <Link 
            to={viewAllLink}
            className="text-orange-500 hover:text-orange-600 text-sm font-medium inline-flex items-center gap-1"
          >
            View all
            <ChevronRight className="ml-1" />
          </Link>
        )}
      </CardContent>
    </Card>
  )
}