import { useState, useMemo, useCallback, type SetStateAction } from 'react';

// Component imports
import MetricCard from '../report/metricCard';
import ChartCard from '../report/chartCard';
import LineChartComponent from '../report/lineChart';
import VerificationChart from '../report/verificationChart';
import TopInvestors from '../report/topInvestors';
import TopProjects from '../report/topProjects';

// Data imports
import { monthOptions, chartDataByMonth } from './chartData';

const Report = () => {
  const [selectedDeveloperMonth, setSelectedDeveloperMonth] = useState('January 2025');
  const [selectedInvestorMonth, setSelectedInvestorMonth] = useState('January 2025');
  const [isDeveloperDropdownOpen, setIsDeveloperDropdownOpen] = useState(false);
  const [isInvestorDropdownOpen, setIsInvestorDropdownOpen] = useState(false);

  const developerSignUpData = useMemo(() =>
    chartDataByMonth[selectedDeveloperMonth as keyof typeof chartDataByMonth] || [],
    [selectedDeveloperMonth]
  );

  const investorSignUpData = useMemo(() =>
    chartDataByMonth[selectedInvestorMonth as keyof typeof chartDataByMonth] || [],
    [selectedInvestorMonth]
  );

  const DeveloperChart = useMemo(() => (
    <LineChartComponent 
      data={developerSignUpData}
      dataKey="developers"
      selectedMonth={selectedDeveloperMonth}
      chartType="developer"
    />
  ), [developerSignUpData, selectedDeveloperMonth]);

  const InvestorChart = useMemo(() => (
    <LineChartComponent 
      data={investorSignUpData}
      dataKey="investors"
      selectedMonth={selectedInvestorMonth}
      chartType="investor"
    />
  ), [investorSignUpData, selectedInvestorMonth]);

  const handleDeveloperMonthSelect = useCallback((month: SetStateAction<string>) => {
    setSelectedDeveloperMonth(month);
    setIsDeveloperDropdownOpen(false);
  }, []);

  const handleInvestorMonthSelect = useCallback((month: SetStateAction<string>) => {
    setSelectedInvestorMonth(month);
    setIsInvestorDropdownOpen(false);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Investment Dashboard</h1>
          <p className="text-gray-600">Overview of platform performance and key metrics</p>
        </div>

        {/* Top Metrics Grid - 3 columns x 3 rows */}
        <div className="grid grid-cols-3 gap-6 mb-8">
          <MetricCard
            title="Total Projects Submitted"
            value="71"
            viewAll={true} subtitle={undefined} trend={undefined} onClick={undefined}          />
          <MetricCard
            title="Total Capital Committed"
            value="$1000000000"
            trend="+2.6% than last month" subtitle={undefined} onClick={undefined}          />
          <MetricCard
            title="Total Funds Disbursed"
            value="$7000000"
            trend="+2.6% than last month" subtitle={undefined} onClick={undefined}          />
          <MetricCard
            title="Total Active Users"
            value="700"
            viewAll={true} subtitle={undefined} trend={undefined} onClick={undefined}          />
          <MetricCard
            title="Percentage of Projects Funded"
            value="120"
            viewAll={true} subtitle={undefined} trend={undefined} onClick={undefined}          />
          <MetricCard
            title="Avg Investor Offer Rate"
            value="1.8"
            subtitle="Per project" trend={undefined} onClick={undefined}          />
          <MetricCard
            title="Total Users Awaiting KYC Approval"
            value="14"
            viewAll={true} subtitle={undefined} trend={undefined} onClick={undefined}          />
          <MetricCard
            title="Projects with Missing Documents"
            value="6"
            viewAll={true} subtitle={undefined} trend={undefined} onClick={undefined}          />
          <MetricCard
            title="Disputed Contracts"
            value="2"
            viewAll={true} subtitle={undefined} trend={undefined} onClick={undefined}          />
        </div>

        {/* Charts Row - 2 columns */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          {/* Developer Sign-ups Chart */}
          <ChartCard 
            title="Developer sign ups"
            selectedMonth={selectedDeveloperMonth}
            isDropdownOpen={isDeveloperDropdownOpen}
            setIsDropdownOpen={setIsDeveloperDropdownOpen}
            handleMonthSelect={handleDeveloperMonthSelect}
            monthOptions={monthOptions}
          >
            {DeveloperChart}
          </ChartCard>

          {/* Investor Sign-ups Chart */}
          <ChartCard 
            title="Investors sign ups"
            selectedMonth={selectedInvestorMonth}
            isDropdownOpen={isInvestorDropdownOpen}
            setIsDropdownOpen={setIsInvestorDropdownOpen}
            handleMonthSelect={handleInvestorMonthSelect}
            monthOptions={monthOptions}
          >
            {InvestorChart}
          </ChartCard>
        </div>

        {/* Bottom Row - 3 columns */}
        <div className="grid grid-cols-3 gap-6">
          <VerificationChart />
          <TopInvestors />
          <TopProjects />
        </div>
      </div>
    </div>
  );
};

export default Report;