{"hash": "90c022de", "configHash": "9737c487", "lockfileHash": "a4de9eb9", "browserHash": "2e1463f1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "267ac8e2", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2b05fdeb", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1a67c6a8", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7b915f29", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "d36f38e7", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "5eea4611", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "5899401e", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "77c0392a", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "4c3f5e21", "needsInterop": false}, "@tanstack/react-table": {"src": "../../@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "9c344b2c", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c671ad38", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "2b93bb25", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "89b38af1", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "bae02ef7", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "209f8586", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "cd7aa66b", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f9dc888d", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "4f4452d6", "needsInterop": false}, "react-icons/ai": {"src": "../../../../node_modules/react-icons/ai/index.mjs", "file": "react-icons_ai.js", "fileHash": "75fc9325", "needsInterop": false}, "recharts": {"src": "../../../../node_modules/recharts/es6/index.js", "file": "recharts.js", "fileHash": "b286b9bd", "needsInterop": false}, "react-chartjs-2": {"src": "../../../../node_modules/react-chartjs-2/dist/index.js", "file": "react-chartjs-2.js", "fileHash": "93897a75", "needsInterop": false}, "chart.js": {"src": "../../../../node_modules/chart.js/dist/chart.js", "file": "chart__js.js", "fileHash": "217b2901", "needsInterop": false}}, "chunks": {"chunk-FXVEBMXU": {"file": "chunk-FXVEBMXU.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-YH7OXAFT": {"file": "chunk-YH7OXAFT.js"}, "chunk-VEOGUWUC": {"file": "chunk-VEOGUWUC.js"}, "chunk-G6IJCT4X": {"file": "chunk-G6IJCT4X.js"}, "chunk-SFVKNZMI": {"file": "chunk-SFVKNZMI.js"}, "chunk-5RANCIOP": {"file": "chunk-5RANCIOP.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}