import React from 'react';
import Layout from './layout'; 
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from '@/pages/home'; 
import Users from '@/pages/users';
import Profile from '@/pages/profilePage'; // Assuming you have a Profile page
import ProjectManagement from '@/pages/project management/project-management';
import ViewProject from '@/pages/project management/view-project';
import FundManagement from '@/pages/fund management/fund-management';
import Contracts from '@/pages/contracts';
import Compliance from '@/pages/compliance';
import Reports from '@/pages/report/reports';
import Settings from '@/pages/settings';
import ViewFunding from '@/pages/fund management/view-funding';

const Root: React.FC = () => {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/users" element={<Users />} />
           <Route path='/profile' element={<Profile />} />
          <Route path="/project-management" element={<ProjectManagement />} />
          <Route path="/project-management/view-project/:id" element={<ViewProject />} />
          <Route path="/fund-management" element={<FundManagement />} />
          <Route path="/fund-management/view-funding/:id" element={<ViewFunding />} />
          <Route path="/contracts" element={<Contracts />} />
          <Route path="/compliance" element={<Compliance />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
    </Router>
  );
};

export default Root;
          
